<meta charset="utf-8" />
<title>
    <?php if (! empty(trim($__env->yieldContent('title')))): ?>
        <?php echo $__env->yieldContent('title'); ?> - <?php echo e(config('app.name')); ?>

    <?php else: ?>
        <?php echo e(config('app.name')); ?> - <?php echo e(config('app.description')); ?>

    <?php endif; ?>
</title>
<meta name="description" content="Code 34, Soluções em Desenvolvimento de Software">
<meta name="keywords" content="Code 34, desenvolvimento de software, agência, marketing digital">
<meta name="author" content="Code 34">
<meta name="viewport" content="width=device-width,initial-scale=1">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="theme-color" content="#322d97">

<!--website-favicon-->
<link href="<?php echo e(asset('images/favicon.ico')); ?>" rel="icon">

<!--plugin-css-->
<link href="<?php echo e(asset('themes/code34/css/bootstrap.min.css')); ?>" rel="stylesheet">
<link href="<?php echo e(asset('themes/code34/css/plugin.min.css')); ?>" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.11.2/css/all.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<!-- template-style-->
<link href="<?php echo e(asset('themes/code34/css/style.css')); ?>" rel="stylesheet">
<link href="<?php echo e(asset('themes/code34/css/responsive.css')); ?>" rel="stylesheet">

<?php if(isset($seo)): ?>
    <?php if(isset($seo['meta_title'])): ?>
        <title><?php echo e($seo['meta_title']); ?></title>
    <?php endif; ?>
    <?php if(isset($seo['meta_description'])): ?>
        <meta name="description" content="<?php echo e($seo['meta_description']); ?>">
    <?php endif; ?>
    <?php if(isset($seo['opengraph'])): ?>
        <?php $__currentLoopData = $seo['opengraph']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <meta property="og:<?php echo e($key); ?>" content="<?php echo e($value); ?>">
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>
<?php endif; ?>

<?php echo $__env->yieldPushContent('css'); ?>
<?php echo $__env->yieldContent('css'); ?>
<?php /**PATH E:\projects\My\code34\resources\themes/code34/partials/head.blade.php ENDPATH**/ ?>