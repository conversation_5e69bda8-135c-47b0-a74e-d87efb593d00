<?php

use function <PERSON><PERSON>\Folio\{middleware, name};

?>


        <div class="container mt-5 pt-5">
            <div class="row">
                <div class="col-lg-12">
                    <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->guest()): ?>
                        <div class="hero-heading-sec text-center mb-5">
                            <h2 class="text-white">
                                <span>Perfil de <?php echo e($this->user->name); ?></span>
                            </h2>
                            <p class="text-white">Visualizando o perfil de <?php echo e('@' . $this->user->username); ?></p>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>

            <div class="row">
                <!-- Profile Card -->
                <div class="col-lg-4 mb-4">
                    <div class="bg-white rounded p-5 text-center">
                        <img src="<?php echo e($this->user->avatar()); ?>" alt="<?php echo e($this->user->name); ?>"
                             class="rounded-circle mx-auto mb-3" style="width: 120px; height: 120px; object-fit: cover;">

                        <h3 class="mb-2"><?php echo e($this->user->name); ?></h3>
                        <p class="text-primary mb-3"><?php echo e('@' . $this->user->username); ?></p>

                        <!--[if BLOCK]><![endif]--><?php if(auth()->check() && auth()->user()->isAdmin()): ?>
                            <a href="<?php echo e(route('impersonate', $this->user->id)); ?>" class="btn btn-sm btn-warning mb-3">
                                <i class="fas fa-user-secret mr-1"></i> Impersonar
                            </a>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <div class="border-top pt-3">
                            <p class="text-muted mb-2">
                                <i class="fas fa-user-tag mr-2"></i>
                                <?php echo e($this->user->roles()->first()->name ?? 'Usuário'); ?>

                            </p>
                            <p class="text-muted">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                Membro desde <?php echo e($this->user->created_at->format('M Y')); ?>

                            </p>
                        </div>
                    </div>
                </div>

                <!-- Profile Content -->
                <div class="col-lg-8">
                    <div class="bg-white rounded p-5">
                        <h4 class="mb-4">Sobre <?php echo e($this->user->name); ?></h4>

                        <!--[if BLOCK]><![endif]--><?php if($this->user->profile('about')): ?>
                            <p class="mb-4"><?php echo e($this->user->profile('about')); ?></p>
                        <?php else: ?>
                            <p class="text-muted mb-4">Este usuário ainda não adicionou uma descrição ao perfil.</p>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <div class="row">
                            <div class="col-md-6">
                                <h6>Informações</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <strong>Nome:</strong> <?php echo e($this->user->name); ?>

                                    </li>
                                    <li class="mb-2">
                                        <strong>Username:</strong> <?php echo e('@' . $this->user->username); ?>

                                    </li>
                                    <li class="mb-2">
                                        <strong>Função:</strong> <?php echo e($this->user->roles()->first()->name ?? 'Usuário'); ?>

                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Atividade</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <strong>Membro desde:</strong> <?php echo e($this->user->created_at->format('d/m/Y')); ?>

                                    </li>
                                    <li class="mb-2">
                                        <strong>Última atualização:</strong> <?php echo e($this->user->updated_at->format('d/m/Y')); ?>

                                    </li>
                                </ul>
                            </div>
                        </div>

                        <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->check()): ?>
                            <!--[if BLOCK]><![endif]--><?php if(auth()->user()->id === $this->user->id): ?>
                                <div class="border-top pt-4 mt-4">
                                    <h6>Ações Rápidas</h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <a href="<?php echo e(route('settings.profile')); ?>" class="btn btn-outline-primary btn-block">
                                                <i class="fas fa-edit mr-2"></i> Editar Perfil
                                            </a>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <a href="<?php echo e(route('settings')); ?>" class="btn btn-outline-secondary btn-block">
                                                <i class="fas fa-cog mr-2"></i> Configurações
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </div>

            <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->guest()): ?>
                <div class="row mt-5">
                    <div class="col-lg-12 text-center">
                        <div class="bg-white rounded p-5">
                            <h5>Junte-se à nossa plataforma</h5>
                            <p class="text-muted">Crie sua conta e comece a usar nossos recursos.</p>
                            <a href="/auth/register" class="btn-main bg-btn lnk">
                                Criar Conta <i class="fas fa-chevron-right fa-icon"></i>
                                <span class="circle"></span>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    <?php /**PATH E:\projects\My\code34\storage\framework\views/7d97481b1fe66f4b51db90da7e794d9f.blade.php ENDPATH**/ ?>