<meta charset="utf-8" />
<title>
    @hasSection('title')
        @yield('title') - {{ config('app.name') }}
    @else
        {{ config('app.name') }} - {{ config('app.description') }}
    @endif
</title>
<meta name="description" content="Code 34, Soluções em Desenvolvimento de Software">
<meta name="keywords" content="Code 34, desenvolvimento de software, agência, marketing digital">
<meta name="author" content="Code 34">
<meta name="viewport" content="width=device-width,initial-scale=1">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="theme-color" content="#322d97">

<!--website-favicon-->
<link href="{{ asset('images/favicon.ico') }}" rel="icon">

<!--plugin-css-->
<link href="{{ asset('themes/code34/assets/css/bootstrap.min.css') }}" rel="stylesheet">
<link href="{{ asset('themes/code34/assets/css/plugin.min.css') }}" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.11.2/css/all.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

<!-- template-style-->
<link href="{{ asset('themes/code34/assets/css/style.css') }}" rel="stylesheet">
<link href="{{ asset('themes/code34/assets/css/responsive.css') }}" rel="stylesheet">

@if(isset($seo))
    @if(isset($seo['meta_title']))
        <title>{{ $seo['meta_title'] }}</title>
    @endif
    @if(isset($seo['meta_description']))
        <meta name="description" content="{{ $seo['meta_description'] }}">
    @endif
    @if(isset($seo['opengraph']))
        @foreach($seo['opengraph'] as $key => $value)
            <meta property="og:{{ $key }}" content="{{ $value }}">
        @endforeach
    @endif
@endif

@stack('css')
@yield('css')
