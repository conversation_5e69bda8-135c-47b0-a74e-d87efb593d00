<?php
    use function Laravel\Folio\{middleware, name};
    use Livewire\Volt\Component;
    use Livewire\Attributes\Computed;
    name('profile');

    new class extends Component
    {
        public $username;

        #[Computed]
        public function user()
        {
            return config('wave.user_model')::where('username', '=', $this->username)->with('roles')->firstOrFail();
        }
    }
?>

<x-dynamic-component :component="((auth()->guest()) ? 'layouts.marketing' : 'layouts.app')">
    @volt('profile')
        <div class="container mt-5 pt-5">
            <div class="row">
                <div class="col-lg-12">
                    @guest
                        <div class="hero-heading-sec text-center mb-5">
                            <h2 class="text-white">
                                <span>Perfil de {{ $this->user->name }}</span>
                            </h2>
                            <p class="text-white">Visualizando o perfil de {{ '@' . $this->user->username }}</p>
                        </div>
                    @endguest
                </div>
            </div>

            <div class="row">
                <!-- Profile Card -->
                <div class="col-lg-4 mb-4">
                    <div class="bg-white rounded p-5 text-center">
                        <img src="{{ $this->user->avatar() }}" alt="{{ $this->user->name }}" 
                             class="rounded-circle mx-auto mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                        
                        <h3 class="mb-2">{{ $this->user->name }}</h3>
                        <p class="text-primary mb-3">{{ '@' . $this->user->username }}</p>
                        
                        @if (auth()->check() && auth()->user()->isAdmin())
                            <a href="{{ route('impersonate', $this->user->id) }}" class="btn btn-sm btn-warning mb-3">
                                <i class="fas fa-user-secret mr-1"></i> Impersonar
                            </a>
                        @endif
                        
                        <div class="border-top pt-3">
                            <p class="text-muted mb-2">
                                <i class="fas fa-user-tag mr-2"></i>
                                {{ $this->user->roles()->first()->name ?? 'Usuário' }}
                            </p>
                            <p class="text-muted">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                Membro desde {{ $this->user->created_at->format('M Y') }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Profile Content -->
                <div class="col-lg-8">
                    <div class="bg-white rounded p-5">
                        <h4 class="mb-4">Sobre {{ $this->user->name }}</h4>
                        
                        @if($this->user->profile('about'))
                            <p class="mb-4">{{ $this->user->profile('about') }}</p>
                        @else
                            <p class="text-muted mb-4">Este usuário ainda não adicionou uma descrição ao perfil.</p>
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <h6>Informações</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <strong>Nome:</strong> {{ $this->user->name }}
                                    </li>
                                    <li class="mb-2">
                                        <strong>Username:</strong> {{ '@' . $this->user->username }}
                                    </li>
                                    <li class="mb-2">
                                        <strong>Função:</strong> {{ $this->user->roles()->first()->name ?? 'Usuário' }}
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Atividade</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <strong>Membro desde:</strong> {{ $this->user->created_at->format('d/m/Y') }}
                                    </li>
                                    <li class="mb-2">
                                        <strong>Última atualização:</strong> {{ $this->user->updated_at->format('d/m/Y') }}
                                    </li>
                                </ul>
                            </div>
                        </div>

                        @auth
                            @if(auth()->user()->id === $this->user->id)
                                <div class="border-top pt-4 mt-4">
                                    <h6>Ações Rápidas</h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <a href="{{ route('settings.profile') }}" class="btn btn-outline-primary btn-block">
                                                <i class="fas fa-edit mr-2"></i> Editar Perfil
                                            </a>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <a href="{{ route('settings') }}" class="btn btn-outline-secondary btn-block">
                                                <i class="fas fa-cog mr-2"></i> Configurações
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @endauth
                    </div>
                </div>
            </div>

            @guest
                <div class="row mt-5">
                    <div class="col-lg-12 text-center">
                        <div class="bg-white rounded p-5">
                            <h5>Junte-se à nossa plataforma</h5>
                            <p class="text-muted">Crie sua conta e comece a usar nossos recursos.</p>
                            <a href="{{ route('register') }}" class="btn-main bg-btn lnk">
                                Criar Conta <i class="fas fa-chevron-right fa-icon"></i>
                                <span class="circle"></span>
                            </a>
                        </div>
                    </div>
                </div>
            @endguest
        </div>
    @endvolt
</x-dynamic-component>
