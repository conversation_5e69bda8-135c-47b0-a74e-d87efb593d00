<?php

use function <PERSON><PERSON>\Folio\{middleware, name};

?>


        <div class="container mt-5 pt-5">
            <div class="row">
                <div class="col-lg-12">
                    |---LINE:24---|@guest
                        <div class="hero-heading-sec text-center mb-5">
                            <h2 class="text-white">
                                <span>Perfil de |---LINE:27---|{{ $this->user->name }}</span>
                            </h2>
                            <p class="text-white">Visualizando o perfil de |---LINE:29---|{{ '@' . $this->user->username }}</p>
                        </div>
                    |---LINE:31---|@endguest
                </div>
            </div>

            <div class="row">
                <!-- Profile Card -->
                <div class="col-lg-4 mb-4">
                    <div class="bg-white rounded p-5 text-center">
                        <img src="|---LINE:39---|{{ $this->user->avatar() }}" alt="|---LINE:39---|{{ $this->user->name }}"
                             class="rounded-circle mx-auto mb-3" style="width: 120px; height: 120px; object-fit: cover;">

                        <h3 class="mb-2">|---LINE:42---|{{ $this->user->name }}</h3>
                        <p class="text-primary mb-3">|---LINE:43---|{{ '@' . $this->user->username }}</p>

                        |---LINE:45---|@if (auth()->check() && auth()->user()->isAdmin())
                            <a href="|---LINE:46---|{{ route('impersonate', $this->user->id) }}" class="btn btn-sm btn-warning mb-3">
                                <i class="fas fa-user-secret mr-1"></i> Impersonar
                            </a>
                        |---LINE:49---|@endif

                        <div class="border-top pt-3">
                            <p class="text-muted mb-2">
                                <i class="fas fa-user-tag mr-2"></i>
                                |---LINE:54---|{{ $this->user->roles()->first()->name ?? 'Usuário' }}
                            </p>
                            <p class="text-muted">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                Membro desde |---LINE:58---|{{ $this->user->created_at->format('M Y') }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Profile Content -->
                <div class="col-lg-8">
                    <div class="bg-white rounded p-5">
                        <h4 class="mb-4">Sobre |---LINE:67---|{{ $this->user->name }}</h4>

                        |---LINE:69---|@if($this->user->profile('about'))
                            <p class="mb-4">|---LINE:70---|{{ $this->user->profile('about') }}</p>
                        |---LINE:71---|@else
                            <p class="text-muted mb-4">Este usuário ainda não adicionou uma descrição ao perfil.</p>
                        |---LINE:73---|@endif

                        <div class="row">
                            <div class="col-md-6">
                                <h6>Informações</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <strong>Nome:</strong> |---LINE:80---|{{ $this->user->name }}
                                    </li>
                                    <li class="mb-2">
                                        <strong>Username:</strong> |---LINE:83---|{{ '@' . $this->user->username }}
                                    </li>
                                    <li class="mb-2">
                                        <strong>Função:</strong> |---LINE:86---|{{ $this->user->roles()->first()->name ?? 'Usuário' }}
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Atividade</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <strong>Membro desde:</strong> |---LINE:94---|{{ $this->user->created_at->format('d/m/Y') }}
                                    </li>
                                    <li class="mb-2">
                                        <strong>Última atualização:</strong> |---LINE:97---|{{ $this->user->updated_at->format('d/m/Y') }}
                                    </li>
                                </ul>
                            </div>
                        </div>

                        |---LINE:103---|@auth
                            |---LINE:104---|@if(auth()->user()->id === $this->user->id)
                                <div class="border-top pt-4 mt-4">
                                    <h6>Ações Rápidas</h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <a href="|---LINE:109---|{{ route('settings.profile') }}" class="btn btn-outline-primary btn-block">
                                                <i class="fas fa-edit mr-2"></i> Editar Perfil
                                            </a>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <a href="|---LINE:114---|{{ route('settings') }}" class="btn btn-outline-secondary btn-block">
                                                <i class="fas fa-cog mr-2"></i> Configurações
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            |---LINE:120---|@endif
                        |---LINE:121---|@endauth
                    </div>
                </div>
            </div>

            |---LINE:126---|@guest
                <div class="row mt-5">
                    <div class="col-lg-12 text-center">
                        <div class="bg-white rounded p-5">
                            <h5>Junte-se à nossa plataforma</h5>
                            <p class="text-muted">Crie sua conta e comece a usar nossos recursos.</p>
                            <a href="/auth/register" class="btn-main bg-btn lnk">
                                Criar Conta <i class="fas fa-chevron-right fa-icon"></i>
                                <span class="circle"></span>
                            </a>
                        </div>
                    </div>
                </div>
            |---LINE:139---|@endguest
        </div>
    |---LINE:141---|